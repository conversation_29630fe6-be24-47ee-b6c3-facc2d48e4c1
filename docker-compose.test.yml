services:
  web-app:
    # user: "1001:1001"
    image: node-app:v0.0.1
    build:
      context: .
      dockerfile: Dockerfile
      network: host
      args:
        # note this strictly from .env file. Alternatively use build-args with compose build
        PG_USERNAME: ${PG_USERNAME}
        PG_PASSWORD: ${PG_PASSWORD}
        PG_HOST: ${PG_HOST}
        PG_PORT: ${PG_PORT}
        PG_DATABASE: ${PG_DATABASE}
    # volumes:
    #   - data:/app/generated-images

    env_file:
      - .env.development
    labels:
      - traefik.enable=true
      - traefik.http.routers.web-app.rule=Host(`coloraria.localhost`)
      - traefik.http.services.web-app.loadbalancer.server.port=80
    networks:
      - traefik-net

networks:
  traefik-net:
    external: true

# volumes:
  # data:
