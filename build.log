#0 building with "default" instance using docker driver

#1 [internal] load build definition from Dockerfile
#1 transferring dockerfile: 3.54kB done
#1 WARN: ConsistentInstructionCasing: Command 'env' should match the case of the command majority (uppercase) (line 60)
#1 DONE 0.0s

#2 [internal] load metadata for docker.io/library/node:22-alpine
#2 DONE 1.8s

#3 [internal] load .dockerignore
#3 transferring context: 2B done
#3 DONE 0.0s

#4 [base 1/1] FROM docker.io/library/node:22-alpine@sha256:1b2479dd35a99687d6638f5976fd235e26c5b37e8122f786fcd5fe231d63de5b
#4 resolve docker.io/library/node:22-alpine@sha256:1b2479dd35a99687d6638f5976fd235e26c5b37e8122f786fcd5fe231d63de5b 0.0s done
#4 DONE 0.0s

#5 [internal] load build context
#5 transferring context: 36.73kB 0.6s done
#5 DONE 0.6s

#6 [builder  3/11] COPY public public
#6 CACHED

#7 [builder  8/11] COPY next.config.mjs next.config.mjs
#7 CACHED

#8 [deps 1/4] RUN apk add --no-cache libc6-compat
#8 CACHED

#9 [builder  2/11] COPY --from=deps /app/node_modules ./node_modules
#9 CACHED

#10 [builder  9/11] COPY postcss.config.js postcss.config.js
#10 CACHED

#11 [builder 10/11] COPY pnpm-lock.yaml pnpm-lock.yaml
#11 CACHED

#12 [builder  1/11] WORKDIR /app
#12 CACHED

#13 [deps 4/4] RUN   if [ -f yarn.lock ]; then yarn --frozen-lockfile;   elif [ -f package-lock.json ]; then npm ci;   elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm i --frozen-lockfile;   else echo "Lockfile not found." && exit 1;   fi
#13 CACHED

#14 [deps 2/4] WORKDIR /app
#14 CACHED

#15 [deps 3/4] COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* ./
#15 CACHED

#16 [builder  5/11] COPY src src
#16 CACHED

#17 [builder  7/11] COPY tsconfig.json tsconfig.json
#17 CACHED

#18 [builder  4/11] COPY assets assets
#18 CACHED

#19 [builder  6/11] COPY package.json package.json
#19 CACHED

#20 [builder 11/11] RUN printenv
#20 CACHED

#21 exporting to image
#21 exporting layers done
#21 exporting manifest sha256:2c2d119a7426969070d8b49b5f0912442d8913b27cd191e5ede008e0fc63b06e done
#21 exporting config sha256:ef1c76637f080c297514b05da693322c5eaed7793b753d206588f17dc6a37207 done
#21 exporting attestation manifest sha256:32b77c01c28b6272b6e9f681cdb7b244cc296726b18666a522cb5abcfa9a04c0 0.0s done
#21 exporting manifest list sha256:75cab4db9429de28f07feefae315c73250fdddc6af72f01e2caaada468eb8876 0.0s done
#21 naming to docker.io/library/node-app:v0.0.1
#21 naming to docker.io/library/node-app:v0.0.1 done
#21 unpacking to docker.io/library/node-app:v0.0.1 0.0s done
#21 DONE 0.1s
#0 building with "default" instance using docker driver

#1 [internal] load build definition from Dockerfile
#1 transferring dockerfile: 3.55kB done
#1 DONE 0.0s

#2 [internal] load metadata for docker.io/library/node:22-alpine
#2 DONE 0.6s

#3 [internal] load .dockerignore
#3 transferring context: 2B done
#3 DONE 0.0s

#4 [base 1/1] FROM docker.io/library/node:22-alpine@sha256:1b2479dd35a99687d6638f5976fd235e26c5b37e8122f786fcd5fe231d63de5b
#4 resolve docker.io/library/node:22-alpine@sha256:1b2479dd35a99687d6638f5976fd235e26c5b37e8122f786fcd5fe231d63de5b 0.0s done
#4 DONE 0.0s

#5 [internal] load build context
#5 transferring context: 36.73kB 0.6s done
#5 DONE 0.6s

#6 [deps 4/4] RUN   if [ -f yarn.lock ]; then yarn --frozen-lockfile;   elif [ -f package-lock.json ]; then npm ci;   elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm i --frozen-lockfile;   else echo "Lockfile not found." && exit 1;   fi
#6 CACHED

#7 [builder  8/11] COPY next.config.mjs next.config.mjs
#7 CACHED

#8 [builder  6/11] COPY package.json package.json
#8 CACHED

#9 [builder  9/11] COPY postcss.config.js postcss.config.js
#9 CACHED

#10 [deps 2/4] WORKDIR /app
#10 CACHED

#11 [builder  1/11] WORKDIR /app
#11 CACHED

#12 [builder  5/11] COPY src src
#12 CACHED

#13 [deps 3/4] COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* ./
#13 CACHED

#14 [builder  4/11] COPY assets assets
#14 CACHED

#15 [builder  3/11] COPY public public
#15 CACHED

#16 [deps 1/4] RUN apk add --no-cache libc6-compat
#16 CACHED

#17 [builder  7/11] COPY tsconfig.json tsconfig.json
#17 CACHED

#18 [builder  2/11] COPY --from=deps /app/node_modules ./node_modules
#18 CACHED

#19 [builder 10/11] COPY pnpm-lock.yaml pnpm-lock.yaml
#19 CACHED

#20 [builder 11/11] RUN printenv
#20 0.290 NODE_VERSION=22.18.0
#20 0.290 YARN_VERSION=1.22.22
#20 0.290 SHLVL=1
#20 0.290 NEXT_PUBLIC_POSTHOG_HOST=https://eu.i.posthog.com
#20 0.290 HOME=/root
#20 0.290 PG_PASSWORD=
#20 0.290 PAYLOAD_SECRET=placeholder
#20 0.290 PG_HOST=
#20 0.290 PG_PORT=
#20 0.290 PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
#20 0.290 NEXT_TELEMETRY_DISABLED=1
#20 0.290 STRIPE_SECRET_KEY=placeholder
#20 0.290 PG_DATABASE=
#20 0.290 NEXT_PUBLIC_HOST=https://www.coloraria.com
#20 0.290 NEXT_PUBLIC_RECAPTCHA_KEY_ID=6LfvLlorAAAAAPDiWTulhtRKFuujfKaR2YYzo5Q-
#20 0.290 PG_USERNAME=
#20 0.290 PWD=/app
#20 0.290 NODE_ENV=production
#20 0.290 NEXT_PUBLIC_POSTHOG_KEY=phc_1dd8iqXdRedejlLBDMWheCQTSXWwvmP0QS7nACzFifh
#20 DONE 0.3s

#21 exporting to image
#21 exporting layers 0.1s done
#21 exporting manifest sha256:ecbb6aea05e3c680cb059d42032511e476aedc1ee90c1d606e5be3ffbdc2876b 0.0s done
#21 exporting config sha256:1ca25c888ee07ead9ebe3b5cbe13c289aba133437dfdb77972c5708aae4678b3 0.0s done
#21 exporting attestation manifest sha256:5ad46343aea2ceb20efb233c5a2603e4fb2dca7ced192d54375e8d7470548ffa 0.0s done
#21 exporting manifest list sha256:750050beed588e30815c378c2569d4084da234f172f2ffa21ea8be8822466300 0.0s done
#21 naming to docker.io/library/node-app:v0.0.1 done
#21 unpacking to docker.io/library/node-app:v0.0.1 0.0s done
#21 DONE 0.2s
#0 building with "default" instance using docker driver

#1 [internal] load build definition from Dockerfile
#1 transferring dockerfile: 3.55kB done
#1 DONE 0.0s

#2 [internal] load metadata for docker.io/library/node:22-alpine
#2 DONE 1.5s

#3 [internal] load .dockerignore
#3 transferring context: 2B done
#3 DONE 0.0s

#4 [base 1/1] FROM docker.io/library/node:22-alpine@sha256:1b2479dd35a99687d6638f5976fd235e26c5b37e8122f786fcd5fe231d63de5b
#4 resolve docker.io/library/node:22-alpine@sha256:1b2479dd35a99687d6638f5976fd235e26c5b37e8122f786fcd5fe231d63de5b 0.0s done
#4 DONE 0.0s

#5 [internal] load build context
#5 transferring context: 36.73kB 0.6s done
#5 DONE 0.6s

#6 [deps 4/4] RUN   if [ -f yarn.lock ]; then yarn --frozen-lockfile;   elif [ -f package-lock.json ]; then npm ci;   elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm i --frozen-lockfile;   else echo "Lockfile not found." && exit 1;   fi
#6 CACHED

#7 [deps 3/4] COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* ./
#7 CACHED

#8 [deps 1/4] RUN apk add --no-cache libc6-compat
#8 CACHED

#9 [builder  5/11] COPY src src
#9 CACHED

#10 [builder 10/11] COPY pnpm-lock.yaml pnpm-lock.yaml
#10 CACHED

#11 [deps 2/4] WORKDIR /app
#11 CACHED

#12 [builder  7/11] COPY tsconfig.json tsconfig.json
#12 CACHED

#13 [builder  1/11] WORKDIR /app
#13 CACHED

#14 [builder  4/11] COPY assets assets
#14 CACHED

#15 [builder  2/11] COPY --from=deps /app/node_modules ./node_modules
#15 CACHED

#16 [builder  8/11] COPY next.config.mjs next.config.mjs
#16 CACHED

#17 [builder  3/11] COPY public public
#17 CACHED

#18 [builder  6/11] COPY package.json package.json
#18 CACHED

#19 [builder  9/11] COPY postcss.config.js postcss.config.js
#19 CACHED

#20 [builder 11/11] RUN printenv
#20 CACHED

#21 exporting to image
#21 exporting layers done
#21 exporting manifest sha256:ecbb6aea05e3c680cb059d42032511e476aedc1ee90c1d606e5be3ffbdc2876b done
#21 exporting config sha256:1ca25c888ee07ead9ebe3b5cbe13c289aba133437dfdb77972c5708aae4678b3 done
#21 exporting attestation manifest sha256:74b4f22c53e15060f48f137caeee43a5c3b9134f8f48892dbca740d1a4f102d2 0.0s done
#21 exporting manifest list sha256:55f9d0a40dda8362d08c3d2f5938ffae3f238e7d5008b6f20b8e566938d7b74b
#21 exporting manifest list sha256:55f9d0a40dda8362d08c3d2f5938ffae3f238e7d5008b6f20b8e566938d7b74b 0.0s done
#21 naming to moby-dangling@sha256:55f9d0a40dda8362d08c3d2f5938ffae3f238e7d5008b6f20b8e566938d7b74b done
#21 unpacking to moby-dangling@sha256:55f9d0a40dda8362d08c3d2f5938ffae3f238e7d5008b6f20b8e566938d7b74b 0.0s done
#21 DONE 0.1s
#0 building with "default" instance using docker driver

#1 [internal] load build definition from Dockerfile
#1 transferring dockerfile: 3.54kB done
#1 WARN: ConsistentInstructionCasing: Command 'env' should match the case of the command majority (uppercase) (line 60)
#1 DONE 0.0s

#2 [internal] load metadata for docker.io/library/node:22-alpine
#2 DONE 2.3s

#3 [internal] load .dockerignore
#3 transferring context: 2B done
#3 DONE 0.0s

#4 [base 1/1] FROM docker.io/library/node:22-alpine@sha256:1b2479dd35a99687d6638f5976fd235e26c5b37e8122f786fcd5fe231d63de5b
#4 resolve docker.io/library/node:22-alpine@sha256:1b2479dd35a99687d6638f5976fd235e26c5b37e8122f786fcd5fe231d63de5b 0.0s done
#4 DONE 0.0s

#5 [internal] load build context
#5 transferring context: 36.73kB 0.5s done
#5 DONE 0.6s

#6 [builder  1/11] WORKDIR /app
#6 CACHED

#7 [deps 1/4] RUN apk add --no-cache libc6-compat
#7 CACHED

#8 [builder  7/11] COPY tsconfig.json tsconfig.json
#8 CACHED

#9 [builder 10/11] COPY pnpm-lock.yaml pnpm-lock.yaml
#9 CACHED

#10 [builder  5/11] COPY src src
#10 CACHED

#11 [builder  6/11] COPY package.json package.json
#11 CACHED

#12 [deps 3/4] COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* ./
#12 CACHED

#13 [builder  9/11] COPY postcss.config.js postcss.config.js
#13 CACHED

#14 [builder  2/11] COPY --from=deps /app/node_modules ./node_modules
#14 CACHED

#15 [builder  3/11] COPY public public
#15 CACHED

#16 [builder  4/11] COPY assets assets
#16 CACHED

#17 [builder  8/11] COPY next.config.mjs next.config.mjs
#17 CACHED

#18 [deps 4/4] RUN   if [ -f yarn.lock ]; then yarn --frozen-lockfile;   elif [ -f package-lock.json ]; then npm ci;   elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm i --frozen-lockfile;   else echo "Lockfile not found." && exit 1;   fi
#18 CACHED

#19 [deps 2/4] WORKDIR /app
#19 CACHED

#20 [builder 11/11] RUN printenv
#20 CACHED

#21 exporting to image
#21 exporting layers done
#21 exporting manifest sha256:2c2d119a7426969070d8b49b5f0912442d8913b27cd191e5ede008e0fc63b06e done
#21 exporting config sha256:ef1c76637f080c297514b05da693322c5eaed7793b753d206588f17dc6a37207 done
#21 exporting attestation manifest sha256:95c700eed57d6685578547b2b20ffb534386b576789a9a812f6eadbf6b4162ad 0.0s done
#21 exporting manifest list sha256:d978aadd7ee9c032b9d6e197658df370d627f4b4fd2e3b649cc0f6dd98255c05 0.0s done
#21 naming to moby-dangling@sha256:d978aadd7ee9c032b9d6e197658df370d627f4b4fd2e3b649cc0f6dd98255c05
#21 naming to moby-dangling@sha256:d978aadd7ee9c032b9d6e197658df370d627f4b4fd2e3b649cc0f6dd98255c05 done
#21 unpacking to moby-dangling@sha256:d978aadd7ee9c032b9d6e197658df370d627f4b4fd2e3b649cc0f6dd98255c05 0.0s done
#21 DONE 0.1s
