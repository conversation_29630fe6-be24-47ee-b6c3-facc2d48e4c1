name: Continue Integration

on:
  pull_request:
    branches: [ "master", "dev" ]

env:
  PG_USERNAME: postgres
  PG_PASSWORD: testpass
  PG_HOST: localhost
  PG_PORT: 5432
  PG_DATABASE: line-art-generator

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Run docker compose
      uses: hoverkraft-tech/compose-action@v2.0.1
      with:
        compose-file: "./docker-compose.ci-postgres.yml"
    - name: Run docker compose
      uses: hoverkraft-tech/compose-action@v2.0.1
      with:
        compose-file: "./docker-compose.ci-app.yml"
